# cli.py
from fastapi import <PERSON><PERSON><PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from cortex_server.routers import apikey, experiments, public, user
from cortex_server.middleware.auth import require_user_key, require_admin_key
from common.config import Settings
from cortex_server.db.database import init_models
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from cortex_server.types.healthcheck import HealthCheckResponse

logger = logging.getLogger("cortex-server")

def create_server() -> tuple[FastAPI, Settings]:
    settings = Settings()
    logger.info(f"Creating server")
    logger.debug(f"Creating server with settings: {settings}")
    
    @asynccontextmanager
    async def lifespan(app: FastAPI) -> AsyncGenerator:
        async with init_models(settings) as db:
            app.state.db = db  # Store db instance in app state
            yield

    app = FastAPI(lifespan=lifespan)

    # CORS configuration
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    

    # Include routers
    app.include_router(
        apikey.router,
        prefix="/api/infra", 
        dependencies=[
            # Depends(require_user_key),
            Depends(get_db)  # Add database dependency here if needed globally
        ]
    )
    app.include_router(user.router, prefix="/api/infra", dependencies=[Depends(require_admin_key)])
    app.include_router(experiments.router, prefix="/api/experiments", dependencies=[Depends(require_user_key)])
    app.include_router(public.router, prefix="/api/public")

    # Add a health check endpoint
    @app.get("/healthz")
    async def health_check() -> HealthCheckResponse:
        logger.info("Health check endpoint called!")
        return HealthCheckResponse(status="healthy")

    return app, settings

# Add this function to get the app from the request
def get_app(request: Request) -> FastAPI:
    return request.app

# Update the get_db dependency to use the app
async def get_db(request: Request) -> AsyncGenerator[AsyncSession, None]:
    async for session in request.app.state.db.get_session():
        yield session


