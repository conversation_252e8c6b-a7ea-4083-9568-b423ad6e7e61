from cortex_server.generated.apikey_orm import ApiKeyORM
from pydantic import BaseModel
import datetime
import uuid

class Api<PERSON><PERSON>(BaseModel):
    token: str
    description: str
    user_id: uuid.UUID

    @classmethod
    def from_orm(cls, orm_obj: 'ApiKeyORM') -> 'ApiKey':
        return cls(
            token=orm_obj.token,
            description=orm_obj.description,
            user_id=orm_obj.user_id,
        )

    def to_orm(self) -> 'ApiKeyORM':
        return ApiKeyORM(
            token=self.token,
            description=self.description,
            user_id=self.user_id,
        )
