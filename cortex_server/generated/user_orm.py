from cortex_server.db.database import Base
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, JSON, LargeBinary, UUID

class UserORM(Base):
    __tablename__ = 'users'

    user_id = Column(UUID, primary_key=True, nullable=False)
    name = Column(String, nullable=False)
    description = Column(String, nullable=False)
    is_admin = Column(Boolean, nullable=False, default=False)
