from cortex_server.generated.user_orm import UserORM
from pydantic import BaseModel
import datetime
import uuid

class User(BaseModel):
    user_id: uuid.UUID
    name: str
    description: str
    is_admin: bool = False

    @classmethod
    def from_orm(cls, orm_obj: 'UserORM') -> 'User':
        return cls(
            user_id=orm_obj.user_id,
            name=orm_obj.name,
            description=orm_obj.description,
            is_admin=orm_obj.is_admin,
        )

    def to_orm(self) -> 'UserORM':
        return UserORM(
            user_id=self.user_id,
            name=self.name,
            description=self.description,
            is_admin=self.is_admin,
        )
