import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete

from cortex_server.db.database import get_db, DbSession, Db
from cortex_server.generated.apikey_orm import ApiKeyORM
from cortex_server.middleware.auth import require_user_key, require_admin_key, get_api_key
from cortex_server.generated.apikey import <PERSON><PERSON><PERSON><PERSON>
from cortex_server.generated.user import User
from cortex_server.generated.user_orm import UserORM
from cortex_server.libs.log import logger
from pydantic import BaseModel

class CreateApiKeyRequest(BaseModel):
    user_id: uuid.UUID
    description: str

class ApiKeyDetails(BaseModel):
    token: str
    description: str
    user: User

router = APIRouter()

@router.get("/locked-room/admin")
async def locked_admin_room(authorized: bool = Depends(require_admin_key)):
    return {"message": "You got in to the admin locked room!"}

@router.get("/locked-room/user")
async def locked_user_room(authorized: bool = Depends(require_user_key)):
    return {"message": "You got in to the user locked room!"}

@router.post("/apikey")
async def create_api_key(
    request: CreateApiKeyRequest,
    db: AsyncSession = Depends(get_db),
    authorized: bool = Depends(require_admin_key)
) -> ApiKey:
    logger.info(f"Creating API key for user {request.user_id}, description: {request.description}")
    api_key = ApiKey(
        token=f"cortex-{uuid.uuid4()}",
        description=request.description,
        user_id=request.user_id
    )
    db.add(api_key.to_orm())
    await db.commit()
    return api_key


@router.delete("/apikey/{token}")
async def delete_api_key(
    token: str,
    db: AsyncSession = Depends(get_db),
    authorized: bool = Depends(require_admin_key)
) -> None:
    # Find the API key where the token matches
    logger.info(f"Deleting API key with token: {token}")
    api_key_orm = await db.execute(
        select(ApiKeyORM).where(ApiKeyORM.token == token)
    )
    api_key_orm = api_key_orm.scalar_one_or_none()
    if not api_key_orm:
        raise HTTPException(status_code=404, detail="API key not found")

    db.delete(api_key_orm)
    await db.commit()


# List API keys (joining with user)
@router.get("/apikey")
async def list_api_keys(
    db: AsyncSession = Depends(get_db),
    authorized: bool = Depends(require_admin_key)
) -> list[ApiKeyDetails]:
    logger.info(f"Listing API keys")
    # We need to explicitly join the ApiKeyORM and UserORM tables because they aren't foreign keys
    stmt = select(ApiKeyORM, UserORM).join(UserORM, ApiKeyORM.user_id == UserORM.user_id)
    result = await db.execute(stmt)
    api_keys = result.fetchall()
    return [
        ApiKeyDetails(
            token=api_key.token,
            description=api_key.description,
            user=User.from_orm(user)
        )
        for api_key, user in api_keys
    ]
