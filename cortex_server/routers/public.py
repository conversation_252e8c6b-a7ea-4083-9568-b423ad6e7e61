import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete

from cortex_server.db.database import get_db, DbSession, Db
from cortex_server.generated.apikey_orm import ApiKeyORM
from cortex_server.generated.user_orm import UserORM
from cortex_server.generated.user import User
from cortex_server.middleware.auth import require_user_key, require_admin_key, get_api_key, get_api_key_if_exists
from cortex_server.generated.apikey import ApiKey

router = APIRouter()

@router.get("/room")
async def public_room():
    return {"message": "You got in to the public room!"}

# If you need the API key details in your endpoint:
@router.get("/whoami")
async def whoami(
        db: AsyncSession = Depends(get_db),
        api_key: ApiKey = Depends(get_api_key_if_exists)
) -> dict:
    if api_key is None:
        return dict(
            is_admin=False,
            name="anonymous",
            description="unauthenticated user"
        )
    # Retrieve the user for the API key
    stmt = select(UserORM).where(UserORM.user_id == api_key.user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    # return dict(
    #     is_admin=api_key.is_admin,
    #     name=api_key.name,
    #     description=api_key.description
    # )
    return dict(
        is_admin=user.is_admin,
        name=user.name,
        description=user.description
    )
