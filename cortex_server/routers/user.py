import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete

from cortex_server.db.database import get_db, DbSession, Db
from cortex_server.generated.apikey_orm import ApiKeyORM
from cortex_server.middleware.auth import require_user_key, require_admin_key, get_api_key
from cortex_server.generated.user import User
from cortex_server.generated.user_orm import UserORM
from cortex_server.generated.apikey import Api<PERSON>ey
from cortex_server.libs.log import logger
from pydantic import BaseModel

class CreateUserRequest(BaseModel):
    name: str
    description: str
    is_admin: bool

router = APIRouter()


@router.post("/user")
async def create_user(
    request: CreateUserRequest,
    db: AsyncSession = Depends(get_db),
    authorized: bool = Depends(require_admin_key)
) -> User:
    logger.info(f"Creating User with name: {request.name}, description: {request.description}, is_admin: {request.is_admin}")
    user = User(
        user_id=uuid.uuid4(),
        name=request.name,
        description=request.description,
        is_admin=request.is_admin
    )
    db.add(user.to_orm())
    await db.commit()
    return user


@router.delete("/user/{user_id}")
async def delete_user(
    user_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    authorized: bool = Depends(require_admin_key)
) -> None:
    logger.info(f"Deleting User with id: {user_id}")
    # Find the API keys for the user
    api_key_orms = await db.execute(
        select(ApiKeyORM).where(ApiKeyORM.user_id == user_id)
    )
    api_key_orm_results = api_key_orms.scalars().all()
    for api_key_orm in api_key_orm_results:
        logger.info(f"Deleting API keys for user {user_id}: {api_key_orm.token}")
        db.delete(api_key_orm)

    user_exec = await db.execute(
        select(UserORM).where(UserORM.user_id == user_id)
    )
    user_orm = user_exec.scalar_one_or_none()
    if not user_orm:
        raise HTTPException(status_code=404, detail="User not found")
    logger.info(f"Found user to delete with id {user_id}")
    db.delete(user_orm)
    await db.commit()

# List users
@router.get("/user")
async def list_users(
    db: AsyncSession = Depends(get_db),
    authorized: bool = Depends(require_admin_key)
) -> list[User]:
    logger.info(f"Listing users")
    exec_cmd = await db.execute(select(UserORM))
    logger.info("Found users")
    results = exec_cmd.scalars().all()
    return [User.from_orm(user) for user in results]

# Get user
@router.get("/user/{user_id}")
async def get_user(
        user_id: uuid.UUID,
        db: AsyncSession = Depends(get_db),
        authorized: bool = Depends(require_admin_key)
) -> User:
    logger.info(f"Getting user with id: {user_id}")
    user_exec = await db.execute(
        select(UserORM).where(UserORM.user_id == user_id)
    )
    user_orm = user_exec.scalar_one_or_none()
    if not user_orm:
        raise HTTPException(status_code=404, detail="User not found")
    return User.from_orm(user_orm)
