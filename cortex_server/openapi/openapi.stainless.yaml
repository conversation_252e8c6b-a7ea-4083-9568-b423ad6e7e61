# yaml-language-server: $schema=https://app.stainless.com/config.schema.json

organization:
  # Name of your organization or company, used to determine the name of the client
  # and headings.
  name: cortex
  docs: http://cortex-server.fly.dev
  contact: <EMAIL>

# `targets` define the output targets and their customization options, such as
# whether to emit the Node SDK and what it's package name should be.
targets:
  python:
    package_name: cortexsdk
    production_repo: armandmcqueen/cortex-py-sdk
    publish:
      pypi: false

# `environments` are a map of the name of the environment (e.g. "sandbox",
# "production") to the corresponding url to use.
environments:
  production: http://cortex-server.fly.dev/

# `resources` define the structure and organization for your API, such as how
# methods and models are grouped together and accessed. See the [configuration
# guide] for more information.
#
# [configuration guide]: https://app.stainless.com/docs/guides/configure#resources
resources:
  api:
    # Subresources define resources that are nested within another for more powerful
    # logical groupings, e.g. `cards.payments`.
    subresources:
      infra:
        subresources:
          locked_room:
            # Configure the methods defined in this resource. Each key in the object is the
            # name of the method and the value is either an endpoint (for example, `get /foo`)
            # or an object with more detail.
            #
            # [reference]: https://app.stainless.com/docs/reference/config#method
            methods:
              retrieve_admin: get /api/infra/locked-room/admin
              retrieve_user: get /api/infra/locked-room/user
          apikey:
            methods:
              create: post /api/infra/apikey
              delete: delete /api/infra/apikey/{token}
              list: get /api/infra/apikey
          user:
            # Configure the models--named types--defined in the resource. Each key in the
            # object is the name of the model and the value is either the name of a schema in
            # `#/components/schemas` or an object with more detail.
            #
            # [reference]: https://app.stainless.com/docs/reference/config#model
            models:
              user: '#/components/schemas/User'
            methods:
              list: get /api/infra/user
              create: post /api/infra/user
              delete: delete /api/infra/user/{user_id}
              retrieve: get /api/infra/user/{user_id}
      experiments:
        methods:
          health_check: get /api/experiments/health
      public:
        methods:
          retrieve_room: get /api/public/room
          whoami: get /api/public/whoami

  healthz:
    methods:
      check: get /healthz

settings:
  # All generated integration tests that hit the prism mock http server are marked
  # as skipped. Removing this setting or setting it to false enables tests, but
  # doing so may result in test failures due to bugs in the test server.
  #
  # [prism mock http server]: https://stoplight.io/open-source/prism
  disable_mock_tests: true
  license: Apache-2.0

# `client_settings` define settings for the API client, such as extra constructor
# arguments (used for authentication), retry behavior, idempotency, etc.
client_settings:
  opts:
    api_key:
      type: string
      nullable: false
      auth:
        security_scheme: APIKeyHeader
      read_env: CORTEX_API_KEY

security:
  - APIKeyHeader: []

# `readme` is used to configure the code snippets that will be rendered in the
# README.md of various SDKs. In particular, you can change the `headline`
# snippet's endpoint and the arguments to call it with.
readme:
  example_requests:
    default:
      type: request
      endpoint: get /api/infra/locked-room/admin
      params: {}
    headline:
      type: request
      endpoint: get /api/infra/locked-room/admin
      params: {}