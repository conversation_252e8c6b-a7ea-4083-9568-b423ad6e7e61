openapi: 3.1.0
info:
  title: FastAPI
  version: 0.1.0
paths:
  /api/infra/locked-room/admin:
    get:
      summary: Locked Admin Room
      operationId: locked_admin_room_api_infra_locked_room_admin_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
        - APIKeyHeader: []
  /api/infra/locked-room/user:
    get:
      summary: Locked User Room
      operationId: locked_user_room_api_infra_locked_room_user_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
        - APIKeyHeader: []
  /api/infra/apikey:
    get:
      summary: List Api Keys
      operationId: list_api_keys_api_infra_apikey_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ApiKeyDetails'
                type: array
                title: Response List Api Keys Api Infra Apikey Get
      security:
        - APIKeyHeader: []
    post:
      summary: Create Api Key
      operationId: create_api_key_api_infra_apikey_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApiKeyRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKey'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - APIKeyHeader: []
  /api/infra/apikey/{token}:
    delete:
      summary: Delete Api Key
      operationId: delete_api_key_api_infra_apikey__token__delete
      security:
        - APIKeyHeader: []
      parameters:
        - name: token
          in: path
          required: true
          schema:
            type: string
            title: Token
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/infra/user:
    get:
      summary: List Users
      operationId: list_users_api_infra_user_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/User'
                type: array
                title: Response List Users Api Infra User Get
      security:
        - APIKeyHeader: []
    post:
      summary: Create User
      operationId: create_user_api_infra_user_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
      security:
        - APIKeyHeader: []
  /api/infra/user/{user_id}:
    delete:
      summary: Delete User
      operationId: delete_user_api_infra_user__user_id__delete
      security:
        - APIKeyHeader: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            title: User Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
    get:
      summary: Get User
      operationId: get_user_api_infra_user__user_id__get
      security:
        - APIKeyHeader: []
      parameters:
        - name: user_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            title: User Id
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
  /api/experiments/health:
    get:
      summary: Health Check
      operationId: health_check_api_experiments_health_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
      security:
        - APIKeyHeader: []
  /api/public/room:
    get:
      summary: Public Room
      operationId: public_room_api_public_room_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema: {}
  /api/public/whoami:
    get:
      summary: Whoami
      operationId: whoami_api_public_whoami_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                title: Response Whoami Api Public Whoami Get
      security:
        - APIKeyHeader: []
  /healthz:
    get:
      summary: Health Check
      operationId: health_check_healthz_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
components:
  schemas:
    ApiKey:
      properties:
        token:
          type: string
          title: Token
        description:
          type: string
          title: Description
        user_id:
          type: string
          format: uuid
          title: User Id
      type: object
      required:
        - token
        - description
        - user_id
      title: ApiKey
    ApiKeyDetails:
      properties:
        token:
          type: string
          title: Token
        description:
          type: string
          title: Description
        user:
          $ref: '#/components/schemas/User'
      type: object
      required:
        - token
        - description
        - user
      title: ApiKeyDetails
    CreateApiKeyRequest:
      properties:
        user_id:
          type: string
          format: uuid
          title: User Id
        description:
          type: string
          title: Description
      type: object
      required:
        - user_id
        - description
      title: CreateApiKeyRequest
    CreateUserRequest:
      properties:
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        is_admin:
          type: boolean
          title: Is Admin
      type: object
      required:
        - name
        - description
        - is_admin
      title: CreateUserRequest
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    HealthCheckResponse:
      properties:
        status:
          type: string
          title: Status
      type: object
      required:
        - status
      title: HealthCheckResponse
    User:
      properties:
        user_id:
          type: string
          format: uuid
          title: User Id
        name:
          type: string
          title: Name
        description:
          type: string
          title: Description
        is_admin:
          type: boolean
          title: Is Admin
          default: false
      type: object
      required:
        - user_id
        - name
        - description
      title: User
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
              - type: string
              - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
        - loc
        - msg
        - type
      title: ValidationError
  securitySchemes:
    APIKeyHeader:
      type: apiKey
      in: header
      name: X-API-Key
