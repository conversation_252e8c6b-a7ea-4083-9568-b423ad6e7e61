import typer
import uvicorn
from common.config import Settings
app = typer.Typer()

@app.command()
def serve(
    reload: bool = typer.Option(True, help="Enable auto-reload on code changes"),
    workers: int = typer.Option(1, help="Number of worker processes"),
):
    settings = Settings()
    """Start the Cortex API server"""
    uvicorn.run(
        "cortex_server.run:app",
        host=settings.CORTEX_SERVER_HOST_INTERNAL,
        port=settings.CORTEX_SERVER_PORT,
        reload=reload,
        workers=workers,
    )

if __name__ == "__main__":
    app()