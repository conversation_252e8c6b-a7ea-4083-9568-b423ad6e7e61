# db.py
from contextlib import asynccontextmanager
from sqlalchemy import select
from common.db import Base, Database
from common.config import Settings
import logging
from typing import AsyncGenerator, Annotated
from fastapi import Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

__all__ = ['Base', 'get_db', 'init_models', 'CortexDatabase']

_db = None

class CortexDatabase(Database):
    async def initialize_admin_user(self):
        """Initialize admin user and API key if it doesn't exist"""
        from cortex_server.generated.apikey_orm import ApiKeyORM
        from cortex_server.generated.user_orm import UserORM
        
        async for session in self.get_session():
            query = select(UserORM).where(UserORM.user_id == self.settings.DB_ROOT_USER_ID)
            result = await session.execute(query)
            root_user = result.scalar_one_or_none()

            if not root_user:
                user = UserORM(
                    user_id=self.settings.DB_ROOT_USER_ID,
                    name="root",
                    description="Root admin user",
                    is_admin=True
                )
                key = ApiKeyORM(
                    token=self.settings.DB_ROOT_KEY,
                    description="Root admin key",
                    user_id=self.settings.DB_ROOT_USER_ID
                )
                session.add(user)
                session.add(key)
                await session.commit()
                logger.info("Created default admin user and key")

@asynccontextmanager
async def init_models(settings: Settings) -> AsyncGenerator[CortexDatabase, None]:
    """Initialize database models and setup initial data."""
    logger.info("Starting database initialization...")
    
    db = CortexDatabase(settings)
    await db.create_all()
    await db.initialize_admin_user()
    
    logger.info("Database initialization completed successfully")
    yield db

# Add this dependency function
async def get_db(request: Request) -> AsyncGenerator[AsyncSession, None]:
    async for session in request.app.state.db.get_session():
        yield session

def get_db_instance(request: Request) -> CortexDatabase:
    return request.app.state.db

# Create type aliases for common dependencies
DbSession = Annotated[AsyncSession, Depends(get_db)]
Db = Annotated[CortexDatabase, Depends(get_db_instance)]

