from fastapi import Security, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>eader
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from cortex_server.db.database import get_db
from cortex_server.generated.apikey import <PERSON><PERSON><PERSON><PERSON>
from cortex_server.generated.apikey_orm import ApiKeyORM
from cortex_server.generated.user import User
from cortex_server.generated.user_orm import UserORM
from cortex_server.libs.log import logger

api_key_header = APIKeyHeader(name="X-API-Key")
optional_api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


async def get_api_key(
        key: str = Security(api_key_header),
        db: AsyncSession = Depends(get_db)
) -> ApiKey:
    """Validate and return API key from database"""
    stmt = select(ApiKeyORM).where(ApiKeyORM.token == key)
    result = await db.execute(stmt)
    api_key = result.scalar_one_or_none()
    if not api_key:
        raise HTTPException(status_code=403, detail="Invalid API key")
    return ApiKey.from_orm(api_key)

async def get_api_key_if_exists(
        key: str | None = Security(optional_api_key_header),
        db: AsyncSession = Depends(get_db)
) -> ApiKey | None:
    """Validate and return API key from database if provided, otherwise return None"""
    if not key:
        return None
        
    stmt = select(ApiKeyORM).where(ApiKeyORM.token == key)
    result = await db.execute(stmt)
    api_key = result.scalar_one_or_none()
    if not api_key:
        return None
    return ApiKey.from_orm(api_key)

def require_key_type(require_admin: bool):
    """Factory for creating key type validators
    Admin keys will pass any check, User keys only pass user checks"""

    async def validate_key_type(
            api_key: ApiKey = Depends(get_api_key),
            db: AsyncSession = Depends(get_db)
    ) -> bool:
        # Retrieve the user for the API key
        stmt = select(UserORM).where(UserORM.user_id == api_key.user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        if require_admin and not user.is_admin:
            raise HTTPException(
                status_code=403,
                detail="Admin only"
            )

        return True

    return validate_key_type


# Create reusable dependencies
require_admin_key = require_key_type(require_admin=True)
require_user_key = require_key_type(require_admin=False)