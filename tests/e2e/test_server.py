from __future__ import annotations

from fastapi.testclient import TestClient
from common.config import Settings
import pytest
from typing import Optional
import uuid
from cortex_server.libs.log import logger


class CortexTestClient:
    """Test client for Cortex API."""
    
    def __init__(self, client: TestClient, api_key: Optional[str] = None):
        self.client = client
        self._api_key = api_key
    
    def set_api_key(self, api_key: str):
        self._api_key = api_key

    @property
    def headers(self) -> dict:
        """Get headers with authorization if api_key is set."""
        if self._api_key:
            return {"X-API-Key": f"{self._api_key}"}
        return {}

    def create_api_key(self, name: str, description: str, is_admin: bool) -> dict:
        """Create a new API key."""
        # TODO: Create the user, then create the API key
        logger.debug(f"Creating user {name}")
        user_creation_response = self.client.post(
            "/api/infra/user",
            json={
                "name": name,
                "description": description,
                "is_admin": is_admin
            },
            headers=self.headers
        )
        logger.debug(f"Response from create_api_key: {user_creation_response.json()}")
        user_creation_response.raise_for_status()
        user_id = user_creation_response.json()["user_id"]

        logger.debug(f"Creating API key with headers: {self.headers}")
        response = self.client.post(
            "/api/infra/apikey",

            json={
                "description": description,
                "user_id": user_id
            },
            headers=self.headers
        )
        logger.debug(f"Response from create_api_key: {response.json()}")
        response.raise_for_status()
        return response.json()
    

    def delete_api_key(self, token: str) -> None:
        """Delete an API key."""
        response = self.client.delete(f"/api/infra/apikey/{token}", headers=self.headers)
        logger.debug(f"Response from delete_api_key: {response.json()}")
        if response.status_code != 204:
            raise Exception(f"Failed to delete API key: {response.json()}")

    def get_whoami(self) -> dict:
        """Get current API key details."""
        response = self.client.get("/api/public/whoami", headers=self.headers)
        logger.debug(f"Response from get_whoami: {response.json()}")
        response.raise_for_status()
        return response.json()

    def access_admin_room(self) -> dict:
        """Try to access the admin locked room."""
        response = self.client.get("/api/infra/locked-room/admin", headers=self.headers)
        logger.debug(f"Response from access_admin_room: {response.json()}")
        response.raise_for_status()
        return response.json()

    def access_user_room(self) -> dict:
        """Try to access the user locked room."""
        response = self.client.get("/api/infra/locked-room/user", headers=self.headers)
        logger.debug(f"Response from access_user_room: {response.json()}")
        response.raise_for_status()
        return response.json()

    def access_public_room(self) -> dict:
        """Access the public room."""
        response = self.client.get("/api/public/room", headers=self.headers)
        logger.debug(f"Response from public room: {response.json()}")
        response.raise_for_status()
        return response.json()


def test_health_check(client_and_settings: tuple[TestClient, Settings]) -> None:
    """Verify the health check endpoint returns expected response."""
    client, settings = client_and_settings
    response = client.get("/healthz")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

@pytest.fixture
def cortex_client(client_and_settings: tuple[TestClient, Settings]) -> CortexTestClient:
    """Create a Cortex test client."""
    client, _ = client_and_settings
    return CortexTestClient(client)

@pytest.fixture
def cortex_root_client(client_and_settings: tuple[TestClient, Settings]) -> CortexTestClient:
    """Create a Cortex test client."""
    client, settings = client_and_settings
    return CortexTestClient(client, settings.DB_ROOT_KEY)

@pytest.fixture
def cortex_admin_client(
    client_and_settings: tuple[TestClient, Settings], 
    admin_key: str
) -> CortexTestClient:
    """Create a Cortex test client."""
    client, _ = client_and_settings
    return CortexTestClient(client, admin_key)

@pytest.fixture
def cortex_user_client(
    client_and_settings: tuple[TestClient, Settings], 
    user_key: str
) -> CortexTestClient:
    """Create a Cortex test client."""
    client, _ = client_and_settings
    return CortexTestClient(client, user_key)

@pytest.fixture
def admin_key(
    cortex_root_client: CortexTestClient, 
    client_and_settings: tuple[TestClient, Settings]
) -> str:
    """Create and return a per-test admin API key for testing."""
    _, settings = client_and_settings
    logger.debug("Creating admin key as fixture")

    response = cortex_root_client.create_api_key(
        name=f"test-admin-{uuid.uuid4()}",
        description="Test admin",
        is_admin=True,
    )
    logger.debug(f"Response from admin_key fixture: {response}")
    return response["token"]

@pytest.fixture
def user_key(
    cortex_root_client: CortexTestClient, 
    client_and_settings: tuple[TestClient, Settings]
) -> str:
    """Create and return a per-test user API key for testing."""
    _, settings = client_and_settings
    logger.debug("Creating user key as fixture")
    response = cortex_root_client.create_api_key(
        name=f"test-user-{uuid.uuid4()}",
        description="Test user",
        is_admin=False,
    )
    logger.debug(f"Response from user_key fixture: {response}")
    return response["token"]


def test_admin_auth_rooms(cortex_admin_client: CortexTestClient) -> None:
    """Test accessing admin endpoints with a valid admin API key."""
    
    # Should be able to access admin room
    logger.info("Testing admin room access with valid admin key")
    response = cortex_admin_client.access_admin_room()
    assert response["message"] == "You got in to the admin locked room!"
    
    # Should also be able to access user room
    logger.info("Testing user room access with valid admin key")
    response = cortex_admin_client.access_user_room()
    assert response["message"] == "You got in to the user locked room!"

    # Should be able to access public room
    logger.info("Testing public room access with admin key")
    response = cortex_admin_client.access_public_room()
    assert response["message"] == "You got in to the public room!"

def test_user_auth_rooms(cortex_user_client: CortexTestClient) -> None:
    """Test accessing endpoints with a user API key."""
    # Should NOT be able to access admin room
    logger.info("Testing admin room access with user key is forbidden")
    with pytest.raises(Exception) as exc_info:
        cortex_user_client.access_admin_room()
    assert exc_info.value.response.status_code == 403
    
    # Should be able to access user room
    logger.info("Testing user room access with user key")
    response = cortex_user_client.access_user_room()
    assert response["message"] == "You got in to the user locked room!"

    # Should be able to access public room
    logger.info("Testing public room access with user key")
    response = cortex_user_client.access_public_room()
    assert response["message"] == "You got in to the public room!"

def test_public_auth_rooms(cortex_client: CortexTestClient) -> None:
    """Test accessing public endpoint without authentication."""
    logger.info("Testing public room access")
    response = cortex_client.access_public_room()
    assert response["message"] == "You got in to the public room!"

    # Should NOT be able to access admin room
    logger.info("Testing admin room access with public key is forbidden")
    with pytest.raises(Exception) as exc_info:
        cortex_client.access_admin_room()
    assert exc_info.value.response.status_code == 403

    # Should NOT be able to access user room
    logger.info("Testing user room access with public key is forbidden")
    with pytest.raises(Exception) as exc_info:
        cortex_client.access_user_room()
    assert exc_info.value.response.status_code == 403
    
    
def test_whoami_endpoint(
        cortex_client: CortexTestClient, 
        cortex_admin_client: CortexTestClient, 
        cortex_user_client: CortexTestClient
    ) -> None:
    """Test the whoami endpoint with different types of keys."""
    # Test with admin key
    logger.info("Testing whoami endpoint with admin key")
    response = cortex_admin_client.get_whoami()
    assert response["is_admin"] is True
    assert response["name"].startswith("test-admin")
    assert response["description"].startswith("Test admin")

    # Test with user key
    logger.info("Testing whoami endpoint with user key")
    response = cortex_user_client.get_whoami()
    assert response["is_admin"] is False
    assert response["name"].startswith("test-user")
    assert response["description"].startswith("Test user")

    # Test with public key
    logger.info("Testing whoami endpoint with public key")
    response = cortex_client.get_whoami()
    assert response["is_admin"] is False
    assert "name" in response
    assert "description" in response

def test_api_key_creation(
        cortex_client: CortexTestClient, 
        cortex_admin_client: CortexTestClient, 
        cortex_user_client: CortexTestClient
    ) -> None:
    """Test the API key creation endpoint."""
    logger.info("Testing API key creation with admin key")
    response = cortex_admin_client.create_api_key(
        name=f"test-api-key-{uuid.uuid4()}",
        description="Test API user",
        is_admin=True,
    )
    assert response["token"] is not None
    assert response["description"].startswith("Test API user")

    # User should not be able to create API key
    logger.info("Testing API key creation with user key is forbidden")
    with pytest.raises(Exception) as exc_info:
        cortex_user_client.create_api_key(
            name=f"test-api-key-{uuid.uuid4()}",
            description="Test API user",
            is_admin=True,
        )
    assert exc_info.value.response.status_code == 403

    # Public should not be able to create API key
    logger.info("Testing API key creation with public key is forbidden")
    with pytest.raises(Exception) as exc_info:
        cortex_client.create_api_key(
            name=f"test-api-key-{uuid.uuid4()}",
            description="Test API user",
            is_admin=True,
        )
    assert exc_info.value.response.status_code == 403

