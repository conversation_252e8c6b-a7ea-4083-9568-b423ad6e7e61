import os
from typing import Iterator
import pytest
from fastapi.testclient import Test<PERSON>lient
import logging
import sys

from common.config import Settings
from cortex_server.server import create_server

# This seems redundant?
def pytest_configure(config):
    """Configure pytest."""
    config.option.log_cli_level = "INFO"
    config.option.log_cli = True

@pytest.fixture(autouse=True)
def configure_logging():
    """Configure logging for all tests."""
    logging.getLogger().setLevel(logging.INFO)
    # Ensure specific loggers are set to INFO
    for logger_name in ["uvicorn", "fastapi", "cortex-server"]:
        logging.getLogger(logger_name).setLevel(logging.INFO)

@pytest.fixture
def client_and_settings() -> Iterator[tuple[TestClient, Settings]]:
    """Create a FastAPI TestClient."""
    app, settings = create_server()
    with TestClient(app, raise_server_exceptions=True) as test_client:
        yield test_client, settings