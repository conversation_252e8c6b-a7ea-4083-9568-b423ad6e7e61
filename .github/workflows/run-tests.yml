name: Run Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: cortex
          POSTGRES_PASSWORD: cortex
          POSTGRES_DB: cortex
        ports:
          - 5433:5432
        # Set health checks to wait until postg<PERSON> has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      - name: Install uv
        run: pip install uv

      - name: Install dependencies
        run: uv sync

      - name: Install project in development mode
        run: uv pip install -e .

      - name: Set environment context
        # We need a starting env for the tests to run correctly
        run: uv run cortexctl env set tests

      - name: Run migrations
        run: uv run cortexctl migration upgrade

      - name: Run tests
        run: uv run cortexctl tests run
