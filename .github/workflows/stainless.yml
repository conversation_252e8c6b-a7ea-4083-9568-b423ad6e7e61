name: Upload OpenAPI spec to <PERSON><PERSON><PERSON>

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

jobs:
  stainless:
    concurrency: upload-openapi-spec-action
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: stainless-api/upload-openapi-spec-action@main
        with:
          stainless_api_key: ${{ secrets.STAINLESS_API_KEY }}
          input_path: 'cortex_server/openapi/openapi.yaml'
          config_path: 'cortex_server/openapi/openapi.stainless.yaml'
          project_name: 'cortex-amq'