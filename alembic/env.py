import asyncio
from logging.config import fileConfig
from sqlalchemy.ext.asyncio import async_engine_from_config
from sqlalchemy import pool
from alembic import context

# Import your configuration and models
from common.config import Settings
from common.db import Database
from cortex_server.db.database import Base

# Dynamically import all ORM models
import importlib
import pkgutil
import cortex_server.generated

# Dynamically import all other ORM modules
for _, module_name, _ in pkgutil.iter_modules(cortex_server.generated.__path__):
    if module_name.endswith('_orm'):
        importlib.import_module(f'cortex_server.generated.{module_name}')

config = context.config
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata


def get_url():
    settings = Settings()
    db = Database(settings)
    return db._get_database_url()


def run_migrations_offline() -> None:
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_migrations_online() -> None:
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = get_url()

    connectable = async_engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


def run_sync_migrations():
    """Wrapper function to run migrations in asyncio loop"""
    asyncio.run(run_migrations_online())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_sync_migrations()