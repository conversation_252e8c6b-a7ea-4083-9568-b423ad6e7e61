"""Make api key details mandatory

Revision ID: a3d691d65b9f
Revises: 786655d86595
Create Date: 2024-11-25 19:22:46.412805

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a3d691d65b9f'
down_revision: Union[str, None] = '786655d86595'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('apikeys', 'name',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('apikeys', 'description',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('apikeys', 'description',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('apikeys', 'name',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
