"""add user table

Revision ID: c216f0db4c9f
Revises: a3d691d65b9f
Create Date: 2025-04-12 20:03:12.896914

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c216f0db4c9f'
down_revision: Union[str, None] = 'a3d691d65b9f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=False),
    sa.Column('is_admin', sa.<PERSON>(), nullable=False),
    sa.PrimaryKeyConstraint('user_id')
    )
    op.add_column('apikeys', sa.Column('user_id', sa.UUID(), nullable=False))
    op.drop_column('apikeys', 'is_admin')
    op.drop_column('apikeys', 'name')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('apikeys', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('apikeys', sa.Column('is_admin', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.drop_column('apikeys', 'user_id')
    op.drop_table('users')
    # ### end Alembic commands ###
