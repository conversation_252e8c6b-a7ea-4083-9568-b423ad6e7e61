"""add api keys table

Revision ID: b5a0c46ecee3
Revises: 
Create Date: 2024-11-03 20:54:31.978415

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b5a0c46ecee3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('apikeys',
    sa.Column('token', sa.String(), nullable=False),
    sa.Column('is_admin', sa.<PERSON>(), nullable=False),
    sa.PrimaryKeyConstraint('token')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('apikeys')
    # ### end Alembic commands ###
