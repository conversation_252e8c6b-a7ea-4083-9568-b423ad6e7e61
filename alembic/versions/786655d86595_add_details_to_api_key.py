"""Add details to api key

Revision ID: 786655d86595
Revises: b5a0c46ecee3
Create Date: 2024-11-25 19:19:56.344508

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '786655d86595'
down_revision: Union[str, None] = 'b5a0c46ecee3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('apikeys', sa.Column('name', sa.String(), nullable=True))
    op.add_column('apikeys', sa.Column('description', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('apikeys', 'description')
    op.drop_column('apikeys', 'name')
    # ### end Alembic commands ###
