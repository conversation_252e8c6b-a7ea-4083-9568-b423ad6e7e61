import time
import warnings

class Timer:
    def __init__(self, name: str = "timer"):
        self.name = name
        self.start_time = None
        self.end_time = None

    def start(self):
        if self.start_time is not None:
            raise ValueError("Timer already started")

        self.start_time = time.time()
        return self

    @property
    def elapsed_time(self):
        if self.start_time is None:
            raise ValueError("Timer not started")
        if self.end_time is None:
            raise ValueError("Timer not ended")
        return self.end_time - self.start_time

    @property
    def completed(self):
        return self.start_time is not None and self.end_time is not None

    def end(self, display: bool = True):
        if self.end_time is not None:
            raise ValueError("Timer already ended")

        self.end_time = time.time()
        if display:

            print(f"Elapsed time for '{self.name}': {self.elapsed_time:.2f} seconds")
        return self


def single_tag_extract(raw_text: str, tag: str) -> str:
    # Given a piece of raw text, extract the content between <tag> and </tag>
    # Error if the start or end tag is not found or if there are multiple instances of the tag
    start_tag = f"<{tag}>"
    end_tag = f"</{tag}>"
    if raw_text.count(start_tag) != 1:
        raise ValueError(f"Expected 1 start tag, got {raw_text.count(start_tag)}")
    if raw_text.count(end_tag) != 1:
        raise ValueError(f"Expected 1 end tag, got {raw_text.count(end_tag)}")
    if start_tag not in raw_text:
        raise ValueError(f"Start tag '{start_tag}' not found")
    if end_tag not in raw_text:
        raise ValueError(f"End tag '{end_tag}' not found")
    if raw_text.index(start_tag) > raw_text.index(end_tag):
        raise ValueError("Start tag found after end tag")

    start_idx = raw_text.index(start_tag) + len(start_tag)
    end_idx = raw_text.index(end_tag)
    return raw_text[start_idx:end_idx]

def supress_warnings():
    # Suppress the specific UserWarning from ebooklib
    warnings.filterwarnings("ignore", message="In the future version we will turn default option ignore_ncx to True.")

    # Suppress the specific FutureWarning from ebooklib
    warnings.filterwarnings("ignore", message="This search incorrectly ignores the root element")
