from __future__ import annotations

from pydantic import BaseModel, ConfigDict
from ebooklib import epub, ITEM_DOCUMENT
import html2text
from typing import List
from pathlib import Path
from misc.book_summarizer.epub_sections import EpubSection
from misc.book_summarizer.chunker import new_extract_chunks, extract_groups_from_text, Lines, partition_large_chunks, Indices
from misc.book_summarizer.utils import supress_warnings
from misc.book_summarizer.summarize import SummaryInput
import json
from misc.book_summarizer.data_utils import random_book_id
CACHE_DIR = Path(__file__).parent / "cache"
CACHE_DIR_BOOKS = CACHE_DIR / "books"

CACHE_DIR_BOOKS.mkdir(parents=True, exist_ok=True)

BOOK_BASE_PATH = Path(__file__).parent / "books"


class Book(BaseModel):
    book_id: str
    epub_path: Path
    # epub_book: epub.EpubBook
    # epub_sections: List[EpubSection]
    markdown: str
    # markdown_lines: List[str]
    # markdown_words: List[str]

    chunk_indices: Indices
    chunks: list[Chunk]

    _epub_book_cache: epub.EpubBook | None = None
    _epub_sections_cache: list[EpubSection] | None = None


    @property
    def epub_book(self) -> epub.EpubBook:
        if self._epub_book_cache is None:
            self._epub_book_cache = epub.read_epub(str(self.epub_path))
        return self._epub_book_cache

    def _extract_sections_from_epub_book(self) -> list[EpubSection]:
        """
        Extract the sections from the epub book
        """
        converter = html2text.HTML2Text()
        converter.body_width = 0  # Disable text wrapping
        converter.ignore_images = True

        sections = [
            EpubSection.get_section_from_spine_item(idx, spine_item, self.epub_book, converter)
            for idx, spine_item
            in enumerate(self.epub_book.spine)
        ]
        sections = [s for s in sections if s is not None]

        return sections

    @property
    def epub_sections(self) -> list[EpubSection]:
        if self._epub_sections_cache is None:
            self._epub_sections_cache = self._extract_sections_from_epub_book()
        return self._epub_sections_cache

    @staticmethod
    def sections_to_markdown(sections: list[EpubSection]) -> str:
        """ Convert the sections into a single large markdown string """
        return "\n\n".join([s.markdown for s in sections])

    @property
    def markdown_lines(self) -> List[str]:
        return [l for l in self.markdown.split("\n") if l.strip()]

    @property
    def markdown_words(self) -> List[str]:
        return [w for w in self.markdown.split() if w.strip()]



class Chunk(BaseModel):
    chunk_id: str
    chunk_idx: int
    lines: Lines

    # text: str
    # num_lines: int
    # num_words: int
    # num_chars: int

    # summaries: dict[str, Summary]

    @property
    def text(self) -> str:
        return "\n\n".join(self.lines)

    @property
    def num_lines(self) -> int:
        return len(self.lines)

    @property
    def num_words(self) -> int:
        return sum(len(l.split()) for l in self.lines)

    @property
    def num_chars(self) -> int:
        return sum(len(l) for l in self.lines)


def maybe_get_from_cache(book_id: str) -> Book | None:
    cache_path = CACHE_DIR_BOOKS / f"{book_id}.json"
    if cache_path.exists():
        print(f"Loading book from cache: {cache_path}")
        return Book(**json.loads(cache_path.read_text()))
    return None

def save_to_cache(book: Book):
    cache_path = CACHE_DIR_BOOKS / f"{book.book_id}.json"
    print(f"Saving book to cache: {cache_path}")
    cache_path.write_text(book.model_dump_json(indent=2))



def summarize_book(
        book_id: str,
        force_recompute: bool = False,
        skip_large_chunk_division: bool = False,
        ) -> Book:
    """
    Main function to summarize a book and save it.
    """

    if not force_recompute: 
        book = maybe_get_from_cache(book_id)
        if book is not None:
            return book

    epub_path = BOOK_BASE_PATH / f"{book_id}.epub"
    book = Book(
        book_id=book_id,
        epub_path=epub_path,
        markdown="",
        chunk_indices=[],
        chunks=[])
    book.markdown = Book.sections_to_markdown(book.epub_sections)
    book.chunk_indices = new_extract_chunks([s.markdown.splitlines() for s in book.epub_sections if len(s.markdown.strip()) > 0], recursively_solve_large_chunks=not skip_large_chunk_division)
    chunks: list[Lines] = extract_groups_from_text(book.markdown_lines, book.chunk_indices)
    book.chunks = [
        Chunk(chunk_id=f"{book_id}_{idx}", chunk_idx=idx, lines=chunk)
        for idx, chunk in enumerate(chunks)
    ]

    save_to_cache(book)
    return book



if __name__ == '__main__':
    supress_warnings()
    book_id = random_book_id(well_known_books=True)
    print(f"Summarizing book: {book_id}")
    book = summarize_book(book_id, force_recompute=False, skip_large_chunk_division=True)


    # print(f"Handling large chunks manually")
    # # TODO: Remove this code, we only are using it so we can curently reuse the cached chunks from a previous run that 

    # new_chunk_indices = partition_large_chunks(book.markdown_lines, book.chunk_indices)
    # book.chunk_indices = new_chunk_indices
    # new_chunks: list[Lines] = extract_groups_from_text(book.markdown_lines, new_chunk_indices)
    # book.chunks = [
    #     Chunk(chunk_id=f"{book_id}_{idx}", chunk_idx=idx, lines=chunk)
    #     for idx, chunk in enumerate(new_chunks)
    # ]

    # for chunk in book.chunks:
    #     print(f"Chunk {chunk.chunk_idx} has {chunk.num_lines} lines and {chunk.num_words} words and {chunk.num_chars} chars")

