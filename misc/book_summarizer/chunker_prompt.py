PROMPT = """
You are tasked with identifying the section/chapter headings in a piece of markdown text. The text may or may not use markdown formatting to indicate headings. Each line of the text is prefixed by its line number.

Here is the markdown text:

<markdown_text>
{{MARKDOWN_TEXT}}
</markdown_text>

Your task is to identify all section/chapter headings in this text. To do this, follow these guidelines:

1. Look for markdown formatting: Lines starting with #, ##, ###, etc., are almost always headings.
2. Look for numbering patterns: Lines starting with numbers followed by periods (e.g., 1., 2., 3.) or roman numerals (I., II., III.) may indicate headings.
3. Look for capitalization and formatting cues: Lines that are all capitalized or have a different format from surrounding text may be headings.
4. Consider context and content: Some headings may not have explicit formatting but can be identified by their content and how they relate to the surrounding text.



Before listing the headings, provide an explanation for why you identified each line as a heading or not for all lines that couple plausibly be headings.

Be thorough in your analysis and make sure to consider all potential headings, even if they're not explicitly formatted as such. If you're unsure about a particular line, include it and explain your reasoning.

In some cases, there may be a single logical heading that spans multiple lines. In such cases, only identify the first line of the heading. The step after this will break up the text into chapters to be summarized so if back-to-back line are part of the same logical heading, they will be grouped together in that step.

Use the following format:

<explanations>
Line [number]: [Explanation] [Decision]
Line [number]: [Explanation] [Decision]
...
</explanations>

List all positively identified headings along with their line numbers. Use the following format for your output:

<headings>
Line [number]: [Heading text]
Line [number]: [Heading text]
...
</headings>

Make sure to include the closing tags for the <explanations> and <headings> sections. If there are no headings, return an empty <headings></headings> tag.

"""

def build_extract_heading_prompt(text: str) -> str:
    return PROMPT.replace("{{MARKDOWN_TEXT}}", text)