from pathlib import Path
from llmlib import LLMClient, TextMessage, Provider, AnthropicModel, Role
import os
from pydantic import BaseModel
import time

ANTHROPIC_API_KEY = os.environ.get("ANTHROPIC_API_KEY")


LLM_COST_DOLLARS = float
def llm_complete(prompt: str, print_cost: bool = False) -> tuple[str, LLM_COST_DOLLARS]:
    # cost_tracker = CostTracker()
    client = LLMClient(Provider.ANTHROPIC, AnthropicModel.CLAUDE_3_5_SONNET_20241022, anthropic_key=ANTHROPIC_API_KEY)
    retry_count = 0
    while True:
        try:
            response = client.chat([TextMessage(content=prompt, role=Role.USER)])
            break
        except Exception as e:
            retry_count += 1
            print(f"Error: {e}")
            print(f"Retrying llm_complete call (attempt {retry_count})...")
            time.sleep(1)
    # print(response.usage)
    prompt_tokens = response.usage["prompt_tokens"]
    input_cost_dollars = 3 / 1_000_000 * prompt_tokens
    completion_tokens = response.usage["completion_tokens"]
    output_cost_dollars = 15 / 1_000_000 * completion_tokens
    total_cost = input_cost_dollars + output_cost_dollars
    # cost_tracker.add(prompt_tokens, completion_tokens, total_cost)
    if print_cost:
        print(f"Cost: ${total_cost:.2f}")
    return response.content, total_cost