from pathlib import Path
from pydantic import BaseModel, ConfigDict
from ebooklib import epub, ITEM_DOCUMENT
import html2text
from typing import List
from pathlib import Path
from rich import inspect, print

from misc.book_summarizer.utils import Timer


class Chapter(BaseModel):
    model_config = ConfigDict(
        # Common configuration options
        frozen=True,  # Makes the model immutable
        strict=True,  # Strict type checking
        extra='forbid',  # Forbid extra attributes
        str_strip_whitespace=True,  # Strip whitespace from strings
        arbitrary_types_allowed=True
    )

    index: int
    doc_id: str
    internal_obj: epub.EpubHtml
    html: str
    markdown: str
    file_name: str
    title: str

    @property
    def html_length(self):
        return len(self.html)

    @property
    def markdown_length(self):
        return len(self.markdown)

    def pretty_print(self):
        print("=== CHAPTER ===")
        print(f"Chapter {self.index} ({self.title})")
        print(f"Doc ID: {self.doc_id}")
        print(f"File name: {self.file_name}")
        print(f"HTML length: {self.html_length}")
        print(f"Markdown length: {self.markdown_length}")
        print(f"Markdown sample: {self.markdown[:100]}")
        print("=== END CHAPTER ===")


def get_chapter_from_spine_item(
    spine_idx: int,
    spine_item,
    book: epub.EpubBook,
    md_converter: html2text.HTML2Text
) -> Chapter | None:

    if isinstance(spine_item, tuple):
        if len(spine_item) != 2:
            raise ValueError(f"Spine item tuple must have 2 elements, got {len(spine_item)}")
        doc_id = spine_item[0]
        is_linear = spine_item[1]
        if not isinstance(doc_id, str):
            raise ValueError(f"Spine item id must be a string, got {type(doc_id)}")
        if is_linear == "yes":
            is_linear = True
        if is_linear == "no":
            is_linear = False
        if not isinstance(is_linear, bool):
            raise ValueError(f"Spine item linear flag must be a bool, got {type(is_linear)}")

        if not is_linear:
            # TODO: Footnotes are useful for summaries, but not for now
            # Looks like footnotes, covers
            print(f"Skipping {spine_item}, not linear")
            return None

        doc = book.get_item_with_id(doc_id)
        if not doc:
            # Can be skipped instead of erroring, but erroring to notice if it happens
            raise ValueError(f"Doc with id {doc_id} not found")

        if doc.get_type() != ITEM_DOCUMENT:

            print(f"Skipping {doc_id}, not an ITEM_DOCUMENT, is a {doc.get_type()}")
            return None

        content = doc.get_content().decode('utf-8')
        markdown = md_converter.handle(content).strip()
        return Chapter(
            index=spine_idx,
            doc_id=doc_id,
            internal_obj=doc,
            html=content,
            markdown=markdown,
            file_name=doc.file_name,
            title=doc.title
        )
    else:
        # doc_id = spine_item.get('idref')
        raise NotImplementedError(f"Non-tuple spine items not supported: {type(spine_item)}")



def get_chapters(
        epub_path: Path
) -> list[Chapter]:
    book = epub.read_epub(epub_path)

    converter = html2text.HTML2Text()
    converter.body_width = 0  # Disable text wrapping
    converter.ignore_images = True

    chapters = [
        get_chapter_from_spine_item(idx, spine_item, book, converter)
        for idx, spine_item
        in enumerate(book.spine)
    ]
    chapters = [c for c in chapters if c is not None]

    return chapters





