from functools import wraps
from typing import TypeV<PERSON>, Callable, ParamSpec, cast
import logging

P = ParamSpec('P')  # Parameter specification
R = TypeVar('R')  # Return type


def with_retry(
        max_attempts: int = 5,
        logger: logging.Logger | None = None,
        retry_exceptions: tuple[type[Exception], ...] = (Exception,)
) -> Callable[[Callable[P, R]], Callable[P, R]]:
    """
    A decorator that retries a function on failure with configurable parameters.

    Args:
        max_attempts: Maximum number of retry attempts before raising the exception
        logger: Optional logger for retry attempts. If None, prints to stdout
        retry_exceptions: Tuple of exception types to retry on

    Returns:
        A decorator function that adds retry logic to the decorated function

    Example:
        @with_retry(max_attempts=3)
        def fetch_data() -> str:
            return potentially_failing_operation()
    """

    def decorator(func: Callable[P, R]) -> Callable[P, R]:
        @wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
            attempt = 1

            while True:
                try:
                    return func(*args, **kwargs)
                except retry_exceptions as e:
                    if attempt >= max_attempts:
                        raise e

                    error_msg = (
                        f"Exception in {func.__name__}, "
                        f"retrying (attempt {attempt} just failed): {str(e)}"
                    )

                    if logger:
                        logger.warning(error_msg)
                    else:
                        print(error_msg)

                    attempt += 1

        return cast(Callable[P, R], wrapper)

    return decorator