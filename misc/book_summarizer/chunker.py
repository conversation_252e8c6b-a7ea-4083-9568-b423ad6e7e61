import random
import uuid
from pathlib import Path
from typing import Literal
import shutil

from sqlalchemy.testing.plugin.plugin_base import warnings

from misc.book_summarizer.data_utils import EPubData, WELL_KNOWN_BOOKS
from misc.book_summarizer.retry import with_retry
from misc.book_summarizer.utils import supress_warnings, single_tag_extract
from misc.book_summarizer.llm import llm_complete
from misc.book_summarizer.parser import get_chapters
from misc.book_summarizer.chunker_prompt import build_extract_heading_prompt
from concurrent.futures import ThreadPoolExecutor
from pydantic import BaseModel

Lines = list[str]
Indices = list[int]

LARGE_CHUNK_THRESHOLD = 20_000


def insert_partitioned_chunk(global_indices: Indices, chunk_idx: int, chunk_partition_inds: Indices) -> Indices:
    """
    Replace a chunk with multiple chunks as part of the recursive chunking process.

    Note that the chunk_partition_inds are relative to the chunk (i.e. the first index is 0) and need to be converted 
    to global scale.
    """
    unchanged_before = global_indices[:chunk_idx]
    unchanged_after = global_indices[chunk_idx + 1:]
    chunk_start = global_indices[chunk_idx]
    chunk_partition_inds_global = [i + chunk_start for i in chunk_partition_inds]
    new_global_indices = unchanged_before + chunk_partition_inds_global + unchanged_after
    return new_global_indices




def partition_large_chunks(text: Lines, initial_global_indices: Indices) -> Indices:
    """
    Identify large chunks and partition them into smaller chunks until there are no more large chunks. Returns 
    the new global indices.
    """
    global_indices = initial_global_indices
    print(f"Initial global indices: {global_indices}")
    while True:
        chunks: list[Lines] = extract_groups_from_text(text, global_indices)
        large_chunk_inds = [i for i, c in enumerate(chunks) if count_chars(c) > LARGE_CHUNK_THRESHOLD]

        if len(large_chunk_inds) == 0:
            # No more large chunks, we are done
            return global_indices
        
        print(f"Found {len(large_chunk_inds)} large chunks")
        chunk_idx_to_partition = large_chunk_inds[0]
        for i in range(5):
            partitions = identify_chunk_boundaries(chunks[chunk_idx_to_partition])
            if len(partitions) > 1:
                global_indices = insert_partitioned_chunk(global_indices, chunk_idx_to_partition, partitions)
                break
            else:
                if i == 4:
                    raise ValueError("Failed to partition large chunk into multiple chunks 5 times")
                print(f"Failed to partition large chunk {chunk_idx_to_partition} into multiple chunks. Retrying ({i+1}/5)")
        




def merge_indices(indices: Indices) -> Indices:
    """
    Merge consecutive indices into a single index (the first index of the group)
    """
    indices = sorted(indices)
    # Handle empty input case
    if not indices: return []

    # Initialize result with first number
    result: Indices = [indices[0]]

    # Iterate through remaining numbers
    for num in indices[1:]:
        # If current number isn't consecutive with last result, add it
        if num > result[-1] + 1:
            result.append(num)

    return result

def extract_groups_from_text(text: Lines, indices: Indices) -> list[Lines]:
    """
    Extract groups from a list of lines based on the indices of the groups
    """
    line_groups = []
    for i, group_start_line_idx in enumerate(indices):
        if i == len(indices) - 1:
            line_group = text[group_start_line_idx:]
        else:
            group_end_line_idx = indices[i + 1]
            line_group = text[group_start_line_idx:group_end_line_idx]
        line_groups.append(line_group)
    return line_groups


def global_indices_from_section_indices(sections: list[Lines], section_indices: list[Indices]) -> Indices:
    """
    Given a list of section and chunk boundaries for each section, convert to a global view and return the chunk boundary
    line indices from a global perspective.
    """
    lines_before_section = 0
    global_indices = []
    for section, section_inds in zip(sections, section_indices):
        for i in section_inds:
            global_indices.append(i + lines_before_section)
        lines_before_section += len(section)
    return global_indices






def new_join_lines(lines: Lines) -> str:
    return "\n\n".join(lines)

def new_count_words(lines: Lines) -> int:
    return len(" ".join(lines).split())

def count_chars(lines: Lines) -> int:
    return sum(len(l) for l in lines)


@with_retry(max_attempts=5)
def llm_identify_chunk_boundaries(lines: Lines) -> Indices:
    """ Identify the header lines in a list of lines of text """

    # TODO: Handle when the section is too long for a single LLM request
    assert len(lines) > 0, "No lines found in section"
    paragraphs_for_llm = [f"Line {i+1}: {l}" for i, l in enumerate(lines)]
    text_for_llm = new_join_lines(paragraphs_for_llm)
    prompt = build_extract_heading_prompt(text_for_llm)
    llm_response, cost = llm_complete(prompt)

    headers_raw = single_tag_extract(llm_response, "headings")

    headers = [h.strip() for h in headers_raw.split("\n") if len(h.strip()) > 0]

    inds = []
    for h in headers:
        # Parse the line number from the header. Example:
        # Line 506: ## Q'
        ind = int(h.split(":")[0].split()[1]) - 1

        inds.append(ind)
    if 0 not in inds:
        inds.insert(0, 0)
    return inds

def llm_identify_chunk_boundaries_multi(lines: Lines, num_runs: int = 3, parallelism: int = 3) -> list[Indices]:
    """
    Run the LLM identify chunk boundaries multiple times in parallel and return the results for ensemble processing.
    """
    with ThreadPoolExecutor(max_workers=parallelism) as executor:
        results = executor.map(llm_identify_chunk_boundaries, [lines] * num_runs)
        results = list(results)
    return results

def ensemble_vote_on_full_list(
        potential_indices: list[Indices], 
        lines: Lines,
        silent: bool = True,
    ) -> Indices | None:
    """
    Ensemble multiple models by voting on which result to use. Votes on the complete result, not on individual indices.
    """
    def fprint(*args, **kwargs):
        if not silent:
            print(*args, **kwargs)

    dupe_map = {}
    for p in potential_indices:
        k = ",".join([str(i) for i in p])
        if k not in dupe_map:
            dupe_map[k] = 0
        dupe_map[k] += 1
    options = [(k, v) for k, v in dupe_map.items()]
    options = sorted(options, key=lambda x: x[1], reverse=True)
    if len(options) == 1:
        fprint("Ensemble: full match")
        return [int(s) for s in options[0][0].split(",")]
    else:
        if options[0][1] > options[1][1]:
            fprint(f"Ensemble: favorite exists ({options[0][1]}). votes: {[o[1] for o in options]}")
            return [int(s) for s in options[0][0].split(",")]
        else:
            chunk_id = str(uuid.uuid4())
            log_dir = Path("/tmp") / "chunker_logs" / chunk_id
            log_dir.mkdir(exist_ok=True, parents=True)
            with open(log_dir / f"chunk.txt", "w") as f:
                f.write(new_join_lines(lines))
            with open(log_dir / f"votes.txt", "w") as f:
                for o in options:
                    f.write(f"{o[0]}: {o[1]}\n")

            fprint(f"Ensemble: no favorite. votes: {[o[1] for o in options]} (logs at {log_dir})")
            return None

def ensemble_intersection(potential_indices: list[Indices]) -> Indices:
    """
    Ensemble multiple models by taking the intersection of the results.
    """
    intersection = set(potential_indices[0])
    for p in potential_indices[1:]:
        intersection = intersection.intersection(set(p))
    return list(intersection)


def ensemble_majority_identified_indices(potential_indices: list[Indices]) -> Indices:
    """
    Ensemble multiple models by taking all indices that appear in more than half of the runs.
    """
    # For every index value identified by any of the runs, count the number of times it appears
    # in the results. Return the indices that appear in more than half of the runs.
    needed_votes = len(potential_indices) // 2 + 1
    dupe_map = {}
    for p in potential_indices:
        for i in p:
            if i not in dupe_map:
                dupe_map[i] = 0
            dupe_map[i] += 1
    majority_indices = [i for i, count in dupe_map.items() if count >= needed_votes]
    return majority_indices



@with_retry(max_attempts=4)
def llm_identify_chunk_boundaries_ensemble(
        lines: Lines,
        ensemble_size: int = 3,
        intersection_or_majority: Literal["intersection", "majority"] = "intersection"
) -> list[int]:
    """
    
    """
    results = llm_identify_chunk_boundaries_multi(lines, num_runs=ensemble_size, parallelism=1)
    potential_indices = [r for r in results]

    voted_indices = ensemble_vote_on_full_list(potential_indices, lines)
    if voted_indices is not None:
        print("Ensemble: full list vote successful")
        return voted_indices
    else:
        print("Ensemble: full list vote failed")

    if intersection_or_majority == "intersection":
        intersection_indices = ensemble_intersection(potential_indices)
        print("Ensemble: falling back to intersection mode")
        return intersection_indices
    else:
        majority_indices = ensemble_majority_identified_indices(potential_indices)
        print("Ensemble: falling back to per-index majority mode")
        return majority_indices

def identify_chunk_boundaries(lines: Lines) -> Indices:
    """
    The external API for identifying chunk boundaries for group of lines.
    """
    fallback_ensemble_mode = "intersection"
    # fallback_ensemble_mode = "majority"
    boundary_inds = llm_identify_chunk_boundaries_ensemble(lines, ensemble_size=3, intersection_or_majority=fallback_ensemble_mode)
    return boundary_inds

def chunk_section(section: Lines, section_idx: int, num_sections: int) -> Indices:
    num_words = new_count_words(section)
    print(f"Chunking section {section_idx+1} of {num_sections} ({num_words} words)")
    boundary_inds = identify_chunk_boundaries(section)
    print(f"Partitioned section {section_idx+1} into {len(boundary_inds)} chunks")
    return boundary_inds

def chunk_sections(
        sections: list[Lines],
        parallelism: int = 4,
) -> list[Indices]:
    """
    Take in a list of sections and return section boundary indices for each section.
    """
    results = []
    if parallelism == 1:
        for ind, lines in enumerate(sections):
            results.append(chunk_section(lines, ind, len(sections)))
    else:
        with ThreadPoolExecutor(max_workers=parallelism) as executor:
            results = list(executor.map(chunk_section, sections, range(len(sections)), [len(sections)] * len(sections)))

    return results

def new_validate_sections(sections: list[Lines]) -> None:
    """
    Validate that the sections are valid/sane and raise an error if they are not.
    """
    for i, section_lines in enumerate(sections):
        if len("".join(section_lines).strip()) == 0:
            raise ValueError(f"Section {i} is empty")
        if len(section_lines) == 0:
            raise ValueError(f"Section {i} has no lines")
        if len(section_lines) == 1:
            print(f"WARNING: Section {i} has only one line")

def new_extract_chunks(sections: list[Lines], recursively_solve_large_chunks: bool = True) -> Indices:
    new_validate_sections(sections)
    indices_per_section = chunk_sections(sections)
    global_indices = global_indices_from_section_indices(sections, indices_per_section)

    print("Initial chunking complete, now handling any large chunks")
    text: Lines = [l for section in sections for l in section]
    if recursively_solve_large_chunks:
        global_indices = partition_large_chunks(text, global_indices)
    return global_indices




if __name__ == '__main__':
    supress_warnings() 