from pathlib import Path
from pydantic import BaseModel
import random
DATA_BASE_PATH = Path(__file__).parent / "test"
BOOKS_PATH_PATH = Path(__file__).parent / "books"

WELL_KNOWN_BOOKS = [
    "salt_fat_acid_heat",
    "the_managers_path",
    "hbrs_10_must_reads_on_communication",
    "ai_superpowers_china_silicon_valley_and_the_new_world_order",
    "zero_to_one_notes_on_startups_or_how_to_build_the_future",
    "secrets_of_sand_hill_road",
    "how_to_win_friends_and_influence_people",
    "who_the_a_method_for_hiring",
]

def random_book_id(well_known_books: bool = True) -> str:
    if well_known_books:
        return WELL_KNOWN_BOOKS[random.randint(0, len(WELL_KNOWN_BOOKS) - 1)]
    else:
        books = [p.stem for p in BOOKS_PATH_PATH.iterdir() if p.suffix == ".epub"]
        print(books)
        return books[random.randint(0, len(books) - 1)]

class EPubData(BaseModel):
    """ Serialization handler for epub markdown and various computed data """
    name: str

    @property
    def base_dir(self) -> Path:
        return DATA_BASE_PATH / self.name

    @property
    def epub_path(self) -> Path:
        return BOOKS_PATH_PATH / f"{self.name}.epub"

    @property
    def chapter_base_dir(self) -> Path:
        return self.base_dir / "chapters"

    def chapter_dir(self, chapter_idx: int) -> Path:
        return self.chapter_base_dir / str(chapter_idx).zfill(3)

    def chapter_raw_markdown_path(self, chapter_idx: int):
        return self.chapter_dir(chapter_idx) / "raw.md"

    def chapter_summary_path(self, chapter_idx: int):
        return self.chapter_dir(chapter_idx) / "summary.md"

    @property
    def book_summary_path(self):
        return self.base_dir / "book_summary.md"

    def get_chapter_raw_markdown(self, chapter_idx: int):
        if not self.chapter_raw_markdown_path(chapter_idx).exists():
            raise RuntimeError("Chapter raw markdown not found")
        with self.chapter_raw_markdown_path(chapter_idx).open("r") as f:
            return f.read()

    def save_chapter_summary(self, chapter_idx: int, summary: str):
        with self.chapter_summary_path(chapter_idx).open("w") as f:
            f.write(summary)

    def clean_dir(self):
        """ Create the base directories and remove any existing content"""

        # confirm that the base dir exists and is a dir
        if not self.base_dir.exists():
            self.base_dir.mkdir(parents=True)

        if not self.base_dir.is_dir():
            raise RuntimeError("BaseDir is not directory")

        if not self.epub_path.exists():
            raise RuntimeError("Epub file not found")

        if not self.chapter_base_dir.exists():
            self.chapter_base_dir.mkdir()

        # Remove all the chapter_dirs
        # TODO: shutil.rmtree
        for chapter_dir in self.chapter_base_dir.iterdir():
            if not chapter_dir.is_dir():
                raise RuntimeError("Non-directory file in chapter base dire")
            for f in chapter_dir.iterdir():
                f.unlink()
            chapter_dir.rmdir()

    def save_chapters_as_markdown(self, chapters: list[str]):
        self.clean_dir()

        for chapter_idx, chapter in enumerate(chapters):

            # Be strict here - the clean_dir should have worked
            self.chapter_dir(chapter_idx).mkdir(exist_ok=False, parents=False)

            with open(self.chapter_raw_markdown_path(chapter_idx), "w") as f:
                f.write(chapter)

    def _get_max_chapter_idx(self):
        max_idx = 0
        for chapter_dir in self.chapter_base_dir.iterdir():
            if not chapter_dir.is_dir():
                raise RuntimeError("Non-directory file in chapter base dire")
            idx = int(chapter_dir.name)
            if idx > max_idx:
                max_idx = idx
        return max_idx

    @property
    def chapter_indices(self) -> list[int]:
        return list(range(0, self._get_max_chapter_idx()))

    def compile_and_save_book_summary(self):
        full_summary = self.full_summary()

        with self.book_summary_path.open("w") as f:
            f.write(full_summary)


    def full_raw_markdown(self):
        full_md = ""
        for idx in self.chapter_indices:
            full_md += "\n\n" + self.get_chapter_raw_markdown(idx)
        return full_md

    def full_summary(self):
        full_summary = ""
        for idx in self.chapter_indices:
            summary_path = self.chapter_summary_path(idx)
            if not summary_path.exists():
                raise RuntimeError(f"Summary for chapter {idx} not found")
            with summary_path.open("r") as f:
                summary = f.read()

            full_summary += "\n\n" + summary
            full_summary += "\n\n---"
        return full_summary



def get_books() -> list[str]:
    book_names = [p.stem for p in BOOKS_PATH_PATH.iterdir() if p.suffix == ".epub"]
    return book_names


if __name__ == "__main__":
    print(get_books())