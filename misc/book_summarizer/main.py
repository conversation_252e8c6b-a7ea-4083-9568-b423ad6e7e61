from pathlib import Path
from pydantic import BaseModel, ConfigDict
from ebooklib import epub, ITEM_DOCUMENT
import html2text
from typing import List
from pathlib import Path
from rich import inspect, print
import warnings
from data_utils import EPubData
from parser import get_chapters
from chapter_summarizer import summarize_chapter
from utils import Timer

def load_and_save_chapters(book: EPubData):
    chapters = get_chapters(book.epub_path)
    book.save_chapters_as_markdown([chapter.markdown for chapter in chapters])

def summarize_and_save_chapter(book: EPubData, chapter_idx: int):
    intro_chapter_raw_md = book.get_chapter_raw_markdown(chapter_idx)
    summary, cost = summarize_chapter(intro_chapter_raw_md, debug=True)
    print("----------")
    print("----------")
    print(summary)
    print(cost)
    book.save_chapter_summary(chapter_idx, summary)

def main():
    book_name = "salt_fat_acid_heat"
    # book_name = "every_page_is_page_one"

    book = EPubData(name=book_name)

    # Comment out after first time for speed
    # load_and_save_chapters(book)

    ## This is slow and expensive
    print(f"Summarizing {book_name}")
    for i in book.chapter_indices:
        print("Summarizing chapter", i+1, "of", book.chapter_indices[-1]+1)
        summarize_and_save_chapter(book, i)
    book.compile_and_save_book_summary()

    print("Raw chars", len(book.full_raw_markdown()))
    print("Raw words", len(book.full_raw_markdown().split()))
    print("Summary chars", len(book.full_summary()))
    print("Summary words", len(book.full_summary().split()))










if __name__ == '__main__':


    main()