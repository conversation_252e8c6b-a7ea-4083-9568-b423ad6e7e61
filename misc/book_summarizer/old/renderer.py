from pydantic import BaseModel
from pathlib import Path

class Section(BaseModel):
    title: str | None = None
    content: str | None = None

class Summary:
    def __init__(self):
        self.title = None
        self.sections = []

    def add_title(self, title: str):
        if self.title is not None:
            raise ValueError("Title already set")
        self.title = title

    def new_section(self):
        section = Section()
        self.sections.append(section)
        return section

    def add_section_title(self, title: str):
        current_section = self.sections[-1]
        if current_section.title is not None:
            raise ValueError("Section title already set")
        current_section.title = title

    def add_section_content(self, content: str):
        current_section = self.sections[-1]
        if current_section.content is not None:
            raise ValueError("Section content already set")
        current_section.content = content

    def validate_is_renderable(self):
        if self.title is None:
            raise ValueError("Title not set")
        for section in self.sections:
            if section.title is None:
                raise ValueError("Section title not set")
            if section.content is None:
                raise ValueError("Section content not set")

    def render(self, file_path: str | Path, overwrite_ok: bool = False):
        self.validate_is_renderable()

        file_path = Path(file_path)
        if file_path.exists() and not overwrite_ok:
            raise ValueError("File already exists")

        with open(file_path, "w") as f:
            f.write(f"# {self.title}\n\n")
            for section in self.sections:
                f.write(f"## {section.title}\n\n")
                f.write(f"{section.content}\n\n")

