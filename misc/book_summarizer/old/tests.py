from llm import llm_complete
from misc.book_summarizer.old.renderer import Summary


def test_llm_complete():
    llm_complete("What is the weather in tokyo?", print_cost=True)

def test_renderer():
    summary = Summary()
    summary.add_title("My Book")
    section = summary.new_section()
    section.title = "Chapter 1"
    section.content = "This is the content of chapter 1"
    section = summary.new_section()
    section.title = "Chapter 2"
    section.content = "This is the content of chapter 2"
    summary.render("test.md", overwrite_ok=True)
    with open("../test.md") as f:
        assert f.read() == "# My Book\n\n## Chapter 1\n\nThis is the content of chapter 1\n\n## Chapter 2\n\nThis is the content of chapter 2\n\n"
