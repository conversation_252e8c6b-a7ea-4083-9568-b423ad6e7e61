# Book Summarizer

## Code

### `main.py`

Entrypoint for testing code

### `data_utils.py`

Shared code for loading and saving test data

### `parser.py`

Library. Loads epubs, converts structure into Chapters. No semantic parsing or chunking.

### `extractor.py`

Library. Semantic chunking of Chapters. 



## Development

To run tests

```
pytest tests.py
```

To see output

```
pytest -s tests.py
```