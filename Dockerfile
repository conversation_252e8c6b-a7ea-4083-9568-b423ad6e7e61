# Use Ubuntu 24.04 LTS as the base image (includes Python 3.12)
FROM ubuntu:24.04

# Prevent timezone prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies and Python 3.12
RUN apt-get update && apt-get install -y \
    python3.12 \
    python3.12-venv \
    python3-pip \
    python3.12-dev \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic links for python and pip
RUN ln -s /usr/bin/python3.12 /usr/local/bin/python \
    && ln -s /usr/bin/python3.12 /usr/local/bin/python3

# Set the working directory
WORKDIR /app

# Create and activate a virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

RUN pip install uv



COPY pyproject.toml README.md ./
COPY common ./common
COPY cortex_server ./cortex_server

# Install project dependencies using uv
RUN uv sync
RUN uv pip install -e .

# Expose the port
EXPOSE 8000

# Run the application
CMD ["uv", "run", "cortexserve"]
