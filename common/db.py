# db.py
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession, AsyncEngine
from sqlalchemy.orm import DeclarativeBase
from common.config import Settings
from typing import AsyncGenerator
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Base class for all ORM models
class Base(DeclarativeBase):
    pass

class Database:
    def __init__(self, settings: Settings, echo: bool = False):
        self.settings = settings
        self.engine = self._create_engine(echo)
        self.session_maker = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )

    def _create_engine(self, echo: bool) -> AsyncEngine:
        """Create SQLAlchemy async engine with configured pool settings"""
        return create_async_engine(
            self._get_database_url(),
            echo=echo,
            pool_size=self.settings.DB_POOL_MAX_SIZE,
            max_overflow=self.settings.DB_POOL_MAX_SIZE - self.settings.DB_POOL_MIN_SIZE,
            pool_pre_ping=True,
        )

    def _get_database_url(self, async_mode: bool = True, include_ssl: bool = True) -> str:
        """Construct database URL from settings"""
        driver = "postgresql+asyncpg" if async_mode else "postgresql"
        base_url = f"{driver}://{self.settings.DB_USER}:{self.settings.DB_PASSWORD}@{self.settings.DB_HOST}:{self.settings.DB_PORT}/{self.settings.DB_NAME}"
        if include_ssl:
            return f"{base_url}?ssl={self.settings.DB_SSL_MODE}"
        return base_url

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get a database session"""
        async with self.session_maker() as session:
            try:
                yield session
                await session.commit()
            except:
                await session.rollback()
                raise

    async def create_all(self):
        """Create all tables"""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)


