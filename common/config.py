from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    DB_HOST: str
    DB_PORT: int
    DB_ROOT_USER_ID: str
    DB_ROOT_KEY: str
    DB_SSL_MODE: str
    DB_POOL_MIN_SIZE: int = 5
    DB_POOL_MAX_SIZE: int = 20

    CORTEX_SERVER_HOST_INTERNAL: str
    CORTEX_SERVER_HOST_EXTERNAL: str
    CORTEX_SERVER_PORT: int
    CORTEX_SERVER_HTTPS_EXTERNAL: bool
    CORTEX_SERVER_PORT_EXTERNAL: int

    model_config = SettingsConfigDict(env_file=".env")

def build_external_url(settings: Settings) -> str:
    if settings.CORTEX_SERVER_HTTPS_EXTERNAL:
        return f"https://{settings.CORTEX_SERVER_HOST_EXTERNAL}:{settings.CORTEX_SERVER_PORT_EXTERNAL}"
    else:
        return f"http://{settings.CORTEX_SERVER_HOST_EXTERNAL}:{settings.CORTEX_SERVER_PORT_EXTERNAL}"

