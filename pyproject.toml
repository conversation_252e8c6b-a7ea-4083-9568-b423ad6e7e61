[project]
name = "cortex-server"
version = "0.1.0"
description = "Server for AI projects and experiments"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    "asyncpg>=0.30.0",
    "cortexsdk",
    "boto3==1.35.99",
    # r2 is broken with newer clients. Should be solveable with config, but I couldn't get it to work
    "docker>=7.1.0",
    "ebooklib>=0.18",
    "fastapi[standard]>=0.115.0",
    "gitpython>=3.1.43",
    "google-genai>=1.11.0",
    "gradio>=5.14.0",
    "greenlet>=3.1.1",
    "html2text>=2024.2.26",
    "invoke>=2.2.0",
    "loguru>=0.7.3",
    "numpy>=2.2.1",
    "pydantic-settings>=2.6.1",
    "pydantic>=2.9.2",
    "pyjwt>=2.9.0",
    "python-dotenv>=1.0.1",
    "rich>=13.8.1",
    "ruamel-yaml>=0.18.6",
    "sqlalchemy>=2.0.36",
    "sse-starlette>=2.1.3",
    "streamlit>=1.42.0",
    "typer>=0.12.5",
    "uvicorn[standard]>=0.31.0",
    "watchdog>=6.0.0",
    "google-cloud-storage>=3.2.0",
    "litellm>=1.74.8",
]

[project.scripts]
cortexserve = "cortex_server.cli:app"
cortexctl = "ctl.ctl:app"

[build-system]
requires = ["flit_core>=3.4,<4"]
build-backend = "flit_core.buildapi"

[tool.flit.module]
name = "cortex_server"

[tool.flit.sdist]
include = ["cortex_server/*"]

[tool.uv]
dev-dependencies = [
    "pytest-xdist>=3.6.1",
    "pytest>=8.3.3",
    "alembic>=1.13.3",
    "mypy>=1.13.0",
    "pylint>=3.3.1",
    "ruff>=0.7.2",
]

[tool.uv.sources]
cortexsdk = { git = "https://github.com/armandmcqueen/cortex-py-sdk.git", rev = "efcb99ba4d9c9a41f8dd0173f97c5156fd24697b" }
