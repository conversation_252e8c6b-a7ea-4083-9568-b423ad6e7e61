# fly.toml app configuration file generated for cortex-server on 2024-11-09T18:03:48-08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'cortex-server'
primary_region = 'sjc'

[build]

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '2gb'
  cpu_kind = 'shared'
  cpus = 1
